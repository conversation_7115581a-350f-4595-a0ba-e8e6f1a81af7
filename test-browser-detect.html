<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>浏览器检测测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        .info-box {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .wechat-only {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .non-wechat-only {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #40a9ff;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <h1>浏览器检测测试</h1>
    
    <div class="info-box">
        <h3>当前浏览器信息：</h3>
        <p><strong>User Agent:</strong> <span id="userAgent"></span></p>
        <p><strong>是否为微信浏览器:</strong> <span id="isWeChat"></span></p>
        <p><strong>是否为移动设备:</strong> <span id="isMobile"></span></p>
    </div>

    <div class="wechat-only" id="wechatContent">
        <h3>微信浏览器专用内容</h3>
        <p>这个内容只在微信浏览器中显示</p>
    </div>

    <div class="non-wechat-only" id="nonWechatContent">
        <h3>非微信浏览器内容</h3>
        <p>这个内容在非微信浏览器中显示</p>
        <button id="exportBtn">导出按钮（微信中隐藏）</button>
        <button id="copyBtn">复制按钮（微信中隐藏）</button>
    </div>

    <script>
        // 检测是否为微信浏览器
        function isWeChatBrowser() {
            const userAgent = navigator.userAgent.toLowerCase();
            return userAgent.includes('micromessenger');
        }

        // 检测是否为移动设备
        function isMobileDevice() {
            const userAgent = navigator.userAgent.toLowerCase();
            return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
        }

        // 更新页面信息
        function updatePageInfo() {
            const isWeChat = isWeChatBrowser();
            const isMobile = isMobileDevice();

            document.getElementById('userAgent').textContent = navigator.userAgent;
            document.getElementById('isWeChat').textContent = isWeChat ? '是' : '否';
            document.getElementById('isMobile').textContent = isMobile ? '是' : '否';

            // 根据浏览器类型显示/隐藏内容
            if (isWeChat) {
                document.getElementById('wechatContent').style.display = 'block';
                document.getElementById('nonWechatContent').style.display = 'none';
            } else {
                document.getElementById('wechatContent').style.display = 'none';
                document.getElementById('nonWechatContent').style.display = 'block';
            }
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', updatePageInfo);

        // 按钮点击事件
        document.getElementById('exportBtn')?.addEventListener('click', function() {
            alert('导出功能被点击');
        });

        document.getElementById('copyBtn')?.addEventListener('click', function() {
            alert('复制功能被点击');
        });
    </script>
</body>
</html>
