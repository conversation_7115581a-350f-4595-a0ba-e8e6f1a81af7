<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信浏览器导出按钮隐藏测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .browser-info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .export-btn, .copy-btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .export-btn:hover, .copy-btn:hover {
            background: #40a9ff;
        }
        .hidden {
            display: none !important;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .status.warning {
            background: #fffbe6;
            border: 1px solid #ffe58f;
            color: #faad14;
        }
        .test-result {
            font-weight: bold;
            font-size: 16px;
            margin-top: 15px;
        }
        .simulate-btn {
            background: #722ed1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .simulate-btn:hover {
            background: #9254de;
        }
    </style>
</head>
<body>
    <h1>微信浏览器导出按钮隐藏功能测试</h1>
    
    <div class="browser-info">
        <h3>当前浏览器信息</h3>
        <p><strong>User Agent:</strong> <span id="userAgent"></span></p>
        <p><strong>是否为微信浏览器:</strong> <span id="isWeChat"></span></p>
        <p><strong>检测结果:</strong> <span id="detectionResult"></span></p>
    </div>

    <div class="test-section">
        <h3>模拟测试</h3>
        <p>点击下面的按钮来模拟不同的浏览器环境：</p>
        <button class="simulate-btn" onclick="simulateWeChat()">模拟微信浏览器</button>
        <button class="simulate-btn" onclick="simulateNormalBrowser()">模拟普通浏览器</button>
        <button class="simulate-btn" onclick="resetToActual()">恢复真实环境</button>
    </div>

    <div class="test-section">
        <h3>ViewTimetable 页面导出按钮测试</h3>
        <p>模拟课程表页面的导出功能：</p>
        
        <!-- 模拟课程表底部导出按钮 -->
        <div style="text-align: right; margin: 16px 0; border: 1px dashed #ccc; padding: 10px;">
            <span>课程表底部导出按钮：</span>
            <a id="tableExportBtn" class="export-btn" style="text-decoration: none;">导出</a>
        </div>

        <!-- 模拟学生弹窗导出按钮 -->
        <div style="border: 1px dashed #ccc; padding: 10px; margin: 10px 0;">
            <span>学生操作弹窗导出按钮：</span>
            <button id="studentExportBtn" class="export-btn">导出</button>
            <button class="copy-btn">删除</button>
            <button class="copy-btn">修改</button>
        </div>

        <!-- 模拟导出模态框复制按钮 -->
        <div style="border: 1px dashed #ccc; padding: 10px; margin: 10px 0;">
            <span>导出模态框复制按钮：</span>
            <button id="modalCopyBtn" class="copy-btn">复制</button>
            <button class="copy-btn">关闭</button>
        </div>
    </div>

    <div class="test-section">
        <h3>Dashboard 页面复制按钮测试</h3>
        <p>模拟今明课程弹窗的复制功能：</p>
        
        <div style="border: 1px dashed #ccc; padding: 10px; margin: 10px 0;">
            <span>今日课程复制按钮：</span>
            <button id="todayCopyBtn" class="copy-btn">复制今日</button>
        </div>

        <div style="border: 1px dashed #ccc; padding: 10px; margin: 10px 0;">
            <span>明日课程复制按钮：</span>
            <button id="tomorrowCopyBtn" class="copy-btn">复制明日</button>
        </div>
    </div>

    <div class="test-section">
        <h3>MergePreview 页面导出按钮测试</h3>
        <p>模拟合并预览页面的导出功能：</p>
        
        <div style="border: 1px dashed #ccc; padding: 10px; margin: 10px 0;">
            <span>日期范围课表导出：</span>
            <a id="dateRangeExportBtn" class="export-btn" style="text-decoration: none;">导出</a>
        </div>

        <div style="border: 1px dashed #ccc; padding: 10px; margin: 10px 0;">
            <span>周固定课表导出：</span>
            <a id="weeklyExportBtn" class="export-btn" style="text-decoration: none;">导出</a>
        </div>
    </div>

    <div class="status" id="testStatus">
        <div class="test-result" id="testResult">等待测试...</div>
    </div>

    <script>
        let isSimulating = false;
        let simulatedWeChat = false;

        // 检测是否为微信浏览器
        function isWeChatBrowser() {
            if (isSimulating) {
                return simulatedWeChat;
            }
            const userAgent = navigator.userAgent.toLowerCase();
            return userAgent.includes('micromessenger');
        }

        // 更新按钮显示状态
        function updateButtonsVisibility() {
            const isWeChat = isWeChatBrowser();
            const buttons = [
                'tableExportBtn',
                'studentExportBtn', 
                'modalCopyBtn',
                'todayCopyBtn',
                'tomorrowCopyBtn',
                'dateRangeExportBtn',
                'weeklyExportBtn'
            ];

            buttons.forEach(id => {
                const btn = document.getElementById(id);
                if (btn) {
                    if (isWeChat) {
                        btn.classList.add('hidden');
                    } else {
                        btn.classList.remove('hidden');
                    }
                }
            });

            updateTestResult(isWeChat);
        }

        // 更新测试结果
        function updateTestResult(isWeChat) {
            const statusDiv = document.getElementById('testStatus');
            const resultDiv = document.getElementById('testResult');
            
            if (isWeChat) {
                statusDiv.className = 'status success';
                resultDiv.textContent = '✅ 测试通过：微信浏览器环境下，所有导出/复制按钮已隐藏';
            } else {
                statusDiv.className = 'status warning';
                resultDiv.textContent = '⚠️ 非微信浏览器环境：所有导出/复制按钮正常显示';
            }
        }

        // 更新页面信息
        function updatePageInfo() {
            const isWeChat = isWeChatBrowser();
            
            document.getElementById('userAgent').textContent = navigator.userAgent;
            document.getElementById('isWeChat').textContent = isWeChat ? '是' : '否';
            document.getElementById('detectionResult').textContent = isSimulating ? 
                (simulatedWeChat ? '模拟微信环境' : '模拟普通浏览器环境') : '真实环境';

            updateButtonsVisibility();
        }

        // 模拟微信浏览器
        function simulateWeChat() {
            isSimulating = true;
            simulatedWeChat = true;
            updatePageInfo();
        }

        // 模拟普通浏览器
        function simulateNormalBrowser() {
            isSimulating = true;
            simulatedWeChat = false;
            updatePageInfo();
        }

        // 恢复真实环境
        function resetToActual() {
            isSimulating = false;
            simulatedWeChat = false;
            updatePageInfo();
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', updatePageInfo);

        // 按钮点击事件
        document.querySelectorAll('.export-btn, .copy-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                if (!this.classList.contains('hidden')) {
                    alert(`${this.textContent}功能被点击`);
                }
            });
        });
    </script>
</body>
</html>
